import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, ExternalLink, Globe } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { urlFor } from "@/lib/sanity";
import { ProjectCard } from "./ProjectCard";

// Define the PortfolioItem interface based on Sanity schema
interface PortfolioItem {
  _id: string;
  title: string;
  slug: { current: string };
  client: string;
  website?: string;
  description: string;
  technologies: string[];
  publishedAt: string;
  mainImage: any; // Sanity image
  additionalImages?: any[]; // Sanity images array
  detailedDescription?: any; // Sanity portable text
}

interface ProjectDetailsProps {
  project: PortfolioItem;
  relatedProjects: PortfolioItem[];
}

export function ProjectDetails({ project, relatedProjects }: ProjectDetailsProps) {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <>
      <motion.section
        className="pt-16 md:pt-24 pb-8 md:pb-12 bg-gradient-to-b from-background to-muted/20 dark:from-background dark:to-background/90"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <motion.div variants={itemVariants} className="flex items-center mb-8">
              <Link to="/portfolio" className="text-muted-foreground hover:text-foreground flex items-center group transition-colors">
                <ArrowLeft className={cn(
                  "h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform",
                  direction === 'rtl' ? 'rotate-180 ml-2 mr-0' : ''
                )} />
                {t('portfolio.projectDetails.backToPortfolio')}
              </Link>
            </motion.div>

            <motion.h1
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold mb-6 text-foreground"
            >
              {project.title}
            </motion.h1>

            <motion.div
              variants={itemVariants}
              className="flex flex-wrap gap-3 mb-8"
            >
              {(project.technologies || []).map((tech) => (
                <Badge key={tech} variant="secondary" className="text-sm font-medium">
                  {tech}
                </Badge>
              ))}
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
            >
              <div className="bg-card/50 p-5 rounded-lg border border-border/50 hover:shadow-md transition-shadow">
                <h3 className="text-lg font-medium mb-2">{t('portfolio.projectDetails.client')}</h3>
                <p className="text-muted-foreground">{project.client}</p>
              </div>
              <div className="bg-card/50 p-5 rounded-lg border border-border/50 hover:shadow-md transition-shadow">
                <h3 className="text-lg font-medium mb-2">{t('portfolio.projectDetails.year')}</h3>
                <p className="text-muted-foreground">
                  {project.publishedAt ? new Date(project.publishedAt).getFullYear() : new Date().getFullYear()}
                </p>
              </div>
              <div className="bg-card/50 p-5 rounded-lg border border-border/50 hover:shadow-md transition-shadow">
                <h3 className="text-lg font-medium mb-2">{t('portfolio.projectDetails.technologies')}</h3>
                <p className="text-muted-foreground">{(project.technologies || []).slice(0, 3).join(", ")}</p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      <motion.section
        className="py-8 md:py-12 bg-muted/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="container max-w-5xl mx-auto">
          <div className="mb-16">
            {/* Main image with browser frame */}
            <div className="rounded-xl overflow-hidden shadow-2xl bg-card border border-border">
              {/* Browser-like frame for website screenshots */}
              <div className="h-10 bg-muted border-b border-border flex items-center px-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-400"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
                {project.website && (
                  <div className="flex items-center ml-4 px-3 py-1 rounded-full bg-muted-foreground/10 text-xs text-muted-foreground max-w-md truncate">
                    <Globe className="h-3 w-3 mr-2 text-primary/80" />
                    {project.website.replace(/^https?:\/\/(www\.)?/, '')}
                  </div>
                )}
              </div>

              {project.mainImage ? (
                <img
                  src={urlFor(project.mainImage).width(1200).url()}
                  alt={project.title}
                  className="w-full h-auto object-cover"
                />
              ) : (
                <div className="w-full h-[400px] flex items-center justify-center bg-muted/30">
                  <div className="text-center">
                    <span className="text-5xl block mb-4">🖥️</span>
                    <span className="text-muted-foreground">Website preview not available</span>
                  </div>
                </div>
              )}
            </div>

            {/* Additional images in gallery with browser frames */}
            {project.additionalImages && project.additionalImages.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                {project.additionalImages.map((image, index) => (
                  <motion.div
                    key={index}
                    className="rounded-xl overflow-hidden shadow-md border border-border"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                  >
                    {/* Mini browser frame */}
                    <div className="h-6 bg-muted border-b border-border flex items-center px-2">
                      <div className="flex gap-1.5">
                        <div className="w-2 h-2 rounded-full bg-red-400"></div>
                        <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                        <div className="w-2 h-2 rounded-full bg-green-400"></div>
                      </div>
                    </div>

                    <img
                      src={urlFor(image).width(600).url()}
                      alt={`${project.title} ${index + 1}`}
                      className="w-full h-auto object-cover"
                    />
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </motion.section>

      <motion.section
        className="py-16 md:py-24"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="mb-12 bg-card p-8 rounded-xl shadow-md border border-border/50">
              <h2 className="text-2xl font-bold mb-6 pb-4 border-b border-border/50">
                {t('portfolio.projectDetails.description')}
              </h2>
              <div className="prose prose-lg dark:prose-invert max-w-none">
                <p className="text-muted-foreground text-lg leading-relaxed">
                  {project.description}
                </p>
              </div>

              {project.website && (
                <Button className="mt-8" asChild>
                  <a href={project.website} target="_blank" rel="noopener noreferrer" className="flex items-center">
                    {t('portfolio.projectDetails.viewLive')}
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>
      </motion.section>

      {/* Related Projects Section */}
      {relatedProjects && relatedProjects.length > 0 && (
        <motion.section
          className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <div className="container">
            <h2 className="text-3xl font-bold mb-12 text-center">
              {t('portfolio.projectDetails.relatedProjects')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {(relatedProjects || []).map((relatedProject, index) => (
                <ProjectCard
                  key={relatedProject._id}
                  project={relatedProject}
                  index={index}
                />
              ))}
            </div>
          </div>
        </motion.section>
      )}
    </>
  );
} 
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { urlFor } from "@/lib/sanity";
import { motion } from "framer-motion";

// Define the PortfolioItem interface based on Sanity schema
interface PortfolioItem {
  _id: string;
  title: string;
  slug: { current: string };
  client: string;
  website?: string;
  description: string;
  technologies: string[];
  publishedAt: string;
  mainImage: any; // Sanity image
  additionalImages?: any[]; // Sanity images array
  detailedDescription?: any; // Sanity portable text
}

interface ProjectCardProps {
  project: PortfolioItem;
  featured?: boolean;
  index?: number;
}

export function ProjectCard({ project, featured = false, index = 0 }: ProjectCardProps) {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={cn(
        "bg-card rounded-xl overflow-hidden border border-border group",
        "hover:shadow-xl transition-all duration-300 relative",
        "transform hover:-translate-y-1",
        featured && "md:col-span-2"
      )}
    >
      <div className="relative overflow-hidden aspect-[16/9]">
        {/* Browser-like frame for website screenshots */}
        <div className="absolute top-0 left-0 right-0 h-8 bg-muted/90 border-b border-border z-10 flex items-center px-3">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 rounded-full bg-red-400"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div className="w-3 h-3 rounded-full bg-green-400"></div>
          </div>
          {project.website && (
            <div className="ml-3 text-xs text-muted-foreground truncate max-w-[180px]">
              {project.website.replace(/^https?:\/\/(www\.)?/, '')}
            </div>
          )}
        </div>

        {project.mainImage ? (
          <div className="pt-8 h-full">
            <img
              src={urlFor(project.mainImage).width(1600).height(900).url()}
              alt={project.title}
              className="object-contain w-full h-full bg-white transition-transform duration-500 group-hover:scale-105"
            />
          </div>
        ) : (
          <div className="w-full h-full pt-8 bg-muted/30 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <span className="text-3xl mb-2">🖥️</span>
              <span className="text-muted-foreground text-sm">Website preview not available</span>
            </div>
          </div>
        )}

        <div className="absolute inset-0 pt-8 bg-gradient-to-t from-black/70 via-black/40 to-transparent opacity-0 group-hover:opacity-90 transition-opacity duration-300"></div>

        {featured && (
          <div className="absolute top-11 right-4 z-10">
            <Badge variant="secondary" className="bg-primary text-primary-foreground hover:bg-primary/90">
              {t('home.caseStudies.subtitle')}
            </Badge>
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex flex-wrap gap-2 mb-4">
          {project.technologies && project.technologies.slice(0, 3).map((tech) => (
            <Badge key={`${project._id}-${tech}`} variant="outline" className="bg-muted/50 text-xs font-medium">
              {tech}
            </Badge>
          ))}
          {project.technologies && project.technologies.length > 3 && (
            <Badge variant="outline" className="bg-muted/50 text-xs font-medium">
              +{project.technologies.length - 3}
            </Badge>
          )}
        </div>

        <h3 className="text-xl font-bold mb-2 text-foreground group-hover:text-primary transition-colors duration-200">
          {project.title}
        </h3>

        <p className="text-muted-foreground mb-5 line-clamp-2">
          {project.description}
        </p>

        <div className="mt-auto pt-2 border-t border-border/50">
          <Button
            variant="link"
            className={cn(
              "p-0 h-auto text-primary font-medium",
              direction === 'rtl' ? 'flex flex-row-reverse' : 'flex'
            )}
            asChild
          >
            <Link to={`/portfolio/${project.slug.current}`}>
              {t('common.viewProject')}
              <ArrowRight className={cn(
                "h-4 w-4 transition-transform duration-300 group-hover:translate-x-1",
                direction === 'rtl' ? 'mr-1.5 rotate-180 group-hover:-translate-x-1' : 'ml-1.5'
              )} />
            </Link>
          </Button>
        </div>
      </div>
    </motion.div>
  );
}

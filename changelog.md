# CodeSafir Changelog

All notable changes to the CodeSafir website project will be documented in this file.

## [Unreleased]

## [0.6.1] - 2024-12-20

### Fixed

- Fixed Portfolio component "Cannot read properties of null (reading 'map')" error by adding proper null checks for portfolioItems and filteredProjects arrays
- Fixed Services component MIME type error by removing problematic lazy loading imports and using direct imports instead
- Fixed ProjectDetails component null map error by adding null checks for project.technologies, project.publishedAt, and relatedProjects arrays
- Enhanced Portfolio filtering logic to handle null/undefined technologies arrays safely
- Improved error handling in Portfolio component for better user experience

### Changed

- Removed console.log statements from codebase while preserving console.error for actual error handling
- Simplified Services component imports by removing Suspense and lazy loading
- Added defensive programming practices to Portfolio component with null checks

## [0.6.0] - 2024-12-20

### Fixed

- Fixed portfolio card consistency by removing automatic featured styling for the first project
- Enhanced portfolio category filtering system with better technology mapping for:
  - Web Development: React, Next.js, Vue, Angular, HTML, CSS, JavaScript, TypeScript, Tailwind
  - E-commerce: Shopify, WooCommerce, Magento, Stripe, PayPal, and commerce-related keywords
  - CMS Solutions: WordPress, Drupal, Strapi, Contentful, Sanity
  - Mobile Apps: React Native, Flutter, Ionic, Swift, Kotlin
- Improved home page recent projects display by fixing data integration between Sanity and local portfolio data
- Enhanced project sorting logic to properly handle projects without dates by using current year as fallback
- Fixed SanityPortfolioProvider to properly merge Sanity data with local fallback data
- Improved project card image display for website screenshots with proper aspect ratio and object-contain styling

### Changed

- Portfolio page now displays all projects in a consistent grid layout without featured project section
- Improved filtering logic to categorize projects based on technologies, client names, and descriptions
- Recent Projects section now shows only Sanity-managed projects (not local fallback data)
- Project card images now use 4:3 aspect ratio with object-contain to better display full website screenshots

### Removed

- Removed "Kings of E-commerce Platform" project from local portfolio data

## [0.5.0] - 2023-08-05

### Added

- Integrated Sanity.io as a headless CMS for portfolio and blog sections
- Created content schemas for portfolio projects, blog posts, authors, and categories
- Added Sanity client for data fetching with local data fallback
- Created setup documentation for Sanity integration

## [0.4.0] - 2023-07-20

### Changed

- Updated portfolio page to include only real client projects with actual website images:
  - Tajara Tech (tajara.tech)
  - Kings of E-commerce (kingsofecommerce.com)
  - Smile Rising (smilerising.com.sa)
  - Bu Hamad Co. (buhamadco.com)
  - Richers KSA (richersksa.info)
  - ZoneArt & Workspace (zoneart.net)
  - Adam's World (adamsworld.ae)
  - Oly Jewels (olyjewels.com)
- Removed all placeholder/fictional portfolio projects
- Added image placeholders for real website screenshots

## [0.3.0] - 2023-07-15

### Added

- Portfolio page with initial client projects
- Created tasks.md to track project progress
- Created changelog.md to document changes

### Changed

- Improved UI/UX design of the About page
- Refactored footer for better UI/UX design
- Removed newsletter section from footer

### Fixed

- Fixed missing translation keys in i18next system
- Ensured all hero sections have proper translations
- Fixed responsive design issues on mobile devices

## [0.2.0] - 2023-06-20

### Added

- Next.js and Python to Technologies section
- Updated logos for Tailwind CSS and Shopify
- Implemented light/dark theme toggle
- Added RTL support for Arabic language

### Changed

- Updated hero section code area to reflect company identity
- Improved Services page to match company identity
- Enhanced UI/UX across all pages

### Fixed

- Fixed navigation issues in mobile view
- Corrected alignment issues in RTL mode
- Fixed theme persistence issues

## [0.1.0] - 2023-05-10

### Added

- Initial project setup with Vite, React 18+, TypeScript 5+
- Configured Tailwind CSS v3+ and shadcn/ui
- Set up i18next for bilingual support (English/Arabic)
- Implemented brand colors (#1A1F71, #00BFA6, #F4F4F4)
- Created basic page structure:
  - Home page with hero section
  - Services page
  - About page
  - Contact page
- Added responsive layout components (Header, Footer)
